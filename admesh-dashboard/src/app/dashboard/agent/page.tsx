"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  Activity, 
  MousePointer, 
  Target, 
  TrendingUp, 
  DollarSign, 
  Key, 
  FileText, 
  BarChart3,
  AlertCircle,
  RefreshCw,
  ArrowRight,
  Calendar,
  Users
} from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";
import { formatDistanceToNow } from "date-fns";

interface DashboardStats {
  totalQueries: number;
  totalClicks: number;
  totalConversions: number;
  conversionRate: number;
  totalEarnings: number;
  thisMonthEarnings: number;
}

interface RecentQuery {
  id: string;
  query: string;
  timestamp: string;
  clicks: number;
  conversions: number;
  status: string;
}

export default function AgentDashboard() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<DashboardStats>({
    totalQueries: 0,
    totalClicks: 0,
    totalConversions: 0,
    conversionRate: 0,
    totalEarnings: 0,
    thisMonthEarnings: 0,
  });
  const [recentQueries, setRecentQueries] = useState<RecentQuery[]>([]);

  const fetchDashboardData = async () => {
    if (!user) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const token = await user.getIdToken();
      
      // Fetch stats and recent queries in parallel
      const [statsResponse, queriesResponse] = await Promise.all([
        fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/agent/stats?time_range=30d`, {
          headers: { Authorization: `Bearer ${token}` },
        }),
        fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/agent/queries?time_range=7d&limit=5`, {
          headers: { Authorization: `Bearer ${token}` },
        }),
      ]);

      if (!statsResponse.ok || !queriesResponse.ok) {
        throw new Error("Failed to fetch dashboard data");
      }

      const [statsData, queriesData] = await Promise.all([
        statsResponse.json(),
        queriesResponse.json(),
      ]);

      setStats({
        totalQueries: statsData.total_queries || 0,
        totalClicks: statsData.total_clicks || 0,
        totalConversions: statsData.total_conversions || 0,
        conversionRate: statsData.conversion_rate || 0,
        totalEarnings: statsData.total_earnings || 0,
        thisMonthEarnings: statsData.this_month_earnings || 0,
      });

      setRecentQueries(queriesData.queries?.slice(0, 5) || []);
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to load dashboard";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, [user]);

  const quickActions = [
    {
      title: "View All Queries",
      description: "Monitor query performance",
      href: "/dashboard/agent/queries",
      icon: Activity,
      color: "blue",
    },
    {
      title: "Check Earnings",
      description: "Track your revenue",
      href: "/dashboard/agent/earnings",
      icon: DollarSign,
      color: "green",
    },
    {
      title: "Manage API Keys",
      description: "Configure integrations",
      href: "/dashboard/agent/api-keys",
      icon: Key,
      color: "purple",
    },
    {
      title: "View Documentation",
      description: "Integration guides",
      href: "/dashboard/agent/docs",
      icon: FileText,
      color: "orange",
    },
  ];

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="space-y-2">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-12 w-12 rounded-lg mb-4" />
                <Skeleton className="h-4 w-20 mb-2" />
                <Skeleton className="h-8 w-16 mb-1" />
                <Skeleton className="h-3 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">Welcome back! Here's your agent performance overview.</p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={fetchDashboardData}
          disabled={loading}
          className="gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>{error}</span>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchDashboardData}
              className="ml-4"
            >
              Try Again
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-0 shadow-sm bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total Queries</p>
                <p className="text-3xl font-bold text-blue-900 dark:text-blue-100">{stats.totalQueries}</p>
                <p className="text-xs text-blue-600/70 dark:text-blue-400/70 mt-1">Last 30 days</p>
              </div>
              <div className="h-12 w-12 bg-blue-500 rounded-lg flex items-center justify-center">
                <Activity className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-0 shadow-sm bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600 dark:text-green-400">Total Clicks</p>
                <p className="text-3xl font-bold text-green-900 dark:text-green-100">{stats.totalClicks}</p>
                <p className="text-xs text-green-600/70 dark:text-green-400/70 mt-1">From all queries</p>
              </div>
              <div className="h-12 w-12 bg-green-500 rounded-lg flex items-center justify-center">
                <MousePointer className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-0 shadow-sm bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Conversions</p>
                <p className="text-3xl font-bold text-purple-900 dark:text-purple-100">{stats.totalConversions}</p>
                <p className="text-xs text-purple-600/70 dark:text-purple-400/70 mt-1">From all clicks</p>
              </div>
              <div className="h-12 w-12 bg-purple-500 rounded-lg flex items-center justify-center">
                <Target className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-0 shadow-sm bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600 dark:text-orange-400">This Month</p>
                <p className="text-3xl font-bold text-orange-900 dark:text-orange-100">${stats.thisMonthEarnings.toFixed(2)}</p>
                <p className="text-xs text-orange-600/70 dark:text-orange-400/70 mt-1">Total: ${stats.totalEarnings.toFixed(2)}</p>
              </div>
              <div className="h-12 w-12 bg-orange-500 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Queries */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Recent Queries</CardTitle>
              <CardDescription>Latest queries handled by your agent</CardDescription>
            </div>
            <Button variant="ghost" size="sm" asChild>
              <Link href="/dashboard/agent/queries">
                View All <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </Button>
          </CardHeader>
          <CardContent className="space-y-4">
            {recentQueries.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No recent queries</p>
              </div>
            ) : (
              recentQueries.map((query) => (
                <div key={query.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{query.query}</p>
                    <p className="text-xs text-muted-foreground">
                      {formatDistanceToNow(new Date(query.timestamp), { addSuffix: true })}
                    </p>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <Badge variant="outline" className="text-xs">
                      {query.clicks} clicks
                    </Badge>
                    <Badge 
                      variant={query.status === "completed" ? "default" : "secondary"}
                      className="text-xs"
                    >
                      {query.status}
                    </Badge>
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common tasks and navigation</CardDescription>
          </CardHeader>
          <CardContent className="grid grid-cols-1 gap-3">
            {quickActions.map((action) => (
              <Button
                key={action.href}
                variant="ghost"
                className="h-auto p-4 justify-start"
                asChild
              >
                <Link href={action.href}>
                  <div className={`h-10 w-10 rounded-lg flex items-center justify-center mr-3 bg-${action.color}-100 dark:bg-${action.color}-900`}>
                    <action.icon className={`h-5 w-5 text-${action.color}-600 dark:text-${action.color}-400`} />
                  </div>
                  <div className="text-left">
                    <p className="font-medium">{action.title}</p>
                    <p className="text-sm text-muted-foreground">{action.description}</p>
                  </div>
                  <ArrowRight className="ml-auto h-4 w-4" />
                </Link>
              </Button>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
