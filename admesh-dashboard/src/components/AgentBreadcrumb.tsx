"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Home } from "lucide-react";

interface BreadcrumbConfig {
  [key: string]: {
    label: string;
    href?: string;
  };
}

const agentBreadcrumbConfig: BreadcrumbConfig = {
  "/dashboard/agent": {
    label: "Dashboard",
    href: "/dashboard/agent",
  },
  "/dashboard/agent/queries": {
    label: "Queries",
    href: "/dashboard/agent/queries",
  },
  "/dashboard/agent/earnings": {
    label: "Earnings",
    href: "/dashboard/agent/earnings",
  },
  "/dashboard/agent/conversions": {
    label: "Conversions",
    href: "/dashboard/agent/conversions",
  },
  "/dashboard/agent/api-keys": {
    label: "API Keys",
    href: "/dashboard/agent/api-keys",
  },
  "/dashboard/agent/profile": {
    label: "Profile",
    href: "/dashboard/agent/profile",
  },
  "/dashboard/agent/docs": {
    label: "Documentation",
    href: "/dashboard/agent/docs",
  },
  "/dashboard/agent/onboarding": {
    label: "Onboarding",
    href: "/dashboard/agent/onboarding",
  },
};

export default function AgentBreadcrumb() {
  const pathname = usePathname();

  // Don't show breadcrumbs on the main dashboard page
  if (pathname === "/dashboard/agent") {
    return null;
  }

  // Generate breadcrumb items based on current path
  const pathSegments = pathname.split("/").filter(Boolean);
  const breadcrumbItems = [];

  // Always start with Dashboard
  breadcrumbItems.push({
    label: "Dashboard",
    href: "/dashboard/agent",
    isLast: false,
  });

  // Build path progressively
  let currentPath = "";
  for (let i = 0; i < pathSegments.length; i++) {
    currentPath += `/${pathSegments[i]}`;
    
    // Skip the base dashboard path since we already added it
    if (currentPath === "/dashboard/agent") {
      continue;
    }

    const config = agentBreadcrumbConfig[currentPath];
    if (config) {
      breadcrumbItems.push({
        label: config.label,
        href: config.href,
        isLast: i === pathSegments.length - 1,
      });
    }
  }

  // Don't render if we only have the dashboard item
  if (breadcrumbItems.length <= 1) {
    return null;
  }

  return (
    <div className="px-6 py-3 border-b bg-muted/30">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard/agent" className="flex items-center gap-1">
                <Home className="h-4 w-4" />
                Dashboard
              </Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          
          {breadcrumbItems.slice(1).map((item, index) => (
            <div key={item.href} className="flex items-center gap-1.5">
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                {item.isLast ? (
                  <BreadcrumbPage>{item.label}</BreadcrumbPage>
                ) : (
                  <BreadcrumbLink asChild>
                    <Link href={item.href || "#"}>{item.label}</Link>
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
            </div>
          ))}
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  );
}
